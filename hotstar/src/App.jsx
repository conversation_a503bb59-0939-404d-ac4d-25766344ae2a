import { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FaSpotify, FaHome, FaSearch, FaBook, FaPlus, FaHeart } from 'react-icons/fa';
import { MdLibraryMusic } from 'react-icons/md';

function App() {
  const [playlists, setPlaylists] = useState([]);
  const [recentlyPlayed, setRecentlyPlayed] = useState([]);
  const [featured, setFeatured] = useState([]);

  useEffect(() => {
    setPlaylists([
      { id: 1, name: 'Liked Songs', description: 'Playlist • 120 songs' },
      { id: 2, name: 'Discover Weekly', description: 'Playlist • Spotify' },
      { id: 3, name: 'Release Radar', description: 'Playlist • Spotify' },
    ]);
    
    setRecentlyPlayed([
      { id: 1, name: 'Daily Mix 1', description: 'Various artists', image: 'https://via.placeholder.com/60' },
      { id: 2, name: 'Chill Hits', description: 'Relaxing music', image: 'https://via.placeholder.com/60' },
      { id: 3, name: 'Rock Classics', description: 'Legendary rock songs', image: 'https://via.placeholder.com/60' },
    ]);
    
    setFeatured([
      { id: 1, name: 'Today\'s Top Hits', description: 'Artist name', image: 'https://via.placeholder.com/60' },
      { id: 2, name: 'New Music Friday', description: 'Artist name', image: 'https://via.placeholder.com/60' },
      { id: 3, name: 'Pop Hits', description: 'Artist name', image: 'https://via.placeholder.com/60' },
    ]);
  }, []);

  return (
    <Container>
      <Sidebar>
        <Logo>
          <FaSpotify />
          <h1>Spotify</h1>
        </Logo>
        <Nav>
          <NavItem active>
            <FaHome />
            <span>Home</span>
          </NavItem>
          <NavItem>
            <FaSearch />
            <span>Search</span>
          </NavItem>
          <NavItem>
            <MdLibraryMusic />
            <span>Your Library</span>
          </NavItem>
        </Nav>
        <Playlists>
          <h2>Playlists</h2>
          <ul>
            {playlists.map(playlist => (
              <li key={playlist.id}>
                <a href="#">{playlist.name}</a>
                <span>{playlist.description}</span>
              </li>
            ))}
          </ul>
        </Playlists>
      </Sidebar>
      <Main>
        <Header>
          <h1>Home</h1>
          <Search>
            <input type="text" placeholder="Search" />
          </Search>
        </Header>
        <Content>
          <Section>
            <h2>Recently Played</h2>
            <ul>
              {recentlyPlayed.map(item => (
                <li key={item.id}></li>
                  <img src={item.image} alt={item.name} />
                  <div>
                    <h3>{item.name}</h3>
                    <p>{item.description}</p>
                  </div>
                </li>
              ))}
            </ul>
          </Section>
          <Section>
            <h2>Featured</h2>
            <ul>
              {featured.map(item => (
                <li key={item.id}></li>
                  <img src={item.image} alt={item.name} />
                  <div>
                    <h3>{item.name}</h3>
                    <p>{item.description}</p>
                  </div>
                </li>
              ))}
            </ul>
          </Section>
        </Content>
      </Main>
    </Container>
  );
}

const Container = styled.div`
  display: flex;
  height: 100vh;
`;

const Sidebar = styled.div`
  width: 250px;
  background: #181818;
  color: white;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 24px;
  font-weight: bold;
`;

const Nav = styled.nav`
  display: flex;
  flex-direction: column;
  gap: 10px;
`;

const NavItem = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  color: ${props => props.active ? '#1ed760' : 'white'};
  &:hover {
    color: #1ed760;
  }
`;

const Playlists = styled.div`
  h2 {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
  }
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  li {
    margin-bottom: 5px;
  }
  a {
    color: white;
    text-decoration: none;
  }
`;

const Main = styled.main`
  flex: 1;
  padding: 20px;
  overflow-y: auto;
`;

const Header = styled.header`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  h1 {
    font-size: 24px;
    font-weight: bold;
  }
`;

const Search = styled.div`
  input {
    border: none;
    border-radius: 5px;
    padding: 10px;
    font-size: 16px;
    outline: none;
    background: #282828;
    color: white;
  }
`;

const Content = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
`;

const Section = styled.section`
  flex: 1;
  h2 {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
  }
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  li {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
  }
  img {
    width: 60px;
    height: 60px;
    border-radius: 5px;
  }
  h3 {
    font-size: 16px;
    font-weight: bold;
    margin: 0;
  }
  p {
    font-size: 14px;
    color: #b3b3b3;
    margin: 0;
  }
`;

export default App;