import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  esbuild: {
    jsxInject: `import React from 'react'`, // Ensure React is injected for JSX
  },
  optimizeDeps: {
    include: ['flowbite-react'], // Ensure flowbite-react is pre-bundled
  },
  build: {
    rollupOptions: {
      external: ['tailwindcss/version.js'], // Mark tailwindcss/version.js as external
    },
  },
})
