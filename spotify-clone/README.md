# Spotify Clone

A modern Spotify clone built with React and Tailwind CSS, featuring a responsive design that closely mimics the original Spotify interface.

## Features

- **Responsive Design**: Adapts to different screen sizes with mobile-first approach
- **Modern UI**: Clean, dark theme matching Spotify's visual design
- **Interactive Components**:
  - Sidebar navigation with playlists
  - Main content area with featured playlists and recently played
  - Bottom music player with controls
  - Hover effects and smooth transitions

## Tech Stack

- **React 19** - Modern React with hooks
- **Vite** - Fast build tool and development server
- **Tailwind CSS** - Utility-first CSS framework
- **Lucide React** - Beautiful icons
- **Vitest** - Fast unit testing framework

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn

### Installation

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

3. Open your browser and navigate to `http://localhost:5173`

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm test` - Run tests
- `npm run lint` - Run ESLint

## Components Overview

### Sidebar
- Navigation menu (Home, Search, Your Library)
- Create Playlist and Liked Songs shortcuts
- Scrollable playlist list
- Responsive (hidden on mobile)

### MainContent
- Header with navigation buttons and user profile
- "Good evening" greeting
- Recently played grid (responsive columns)
- "Made for you" section with playlist cards
- Hover effects with play buttons

### Player
- Currently playing song info
- Player controls (shuffle, previous, play/pause, next, repeat)
- Progress bar
- Volume controls
- Responsive layout

## Styling

The app uses a custom Tailwind configuration with Spotify-inspired colors:
- `spotify-green`: #1db954
- `spotify-black`: #191414
- `spotify-dark-gray`: #121212
- `spotify-gray`: #282828
- `spotify-light-gray`: #b3b3b3

## Testing

The project includes unit tests using Vitest and React Testing Library. Run tests with:

```bash
npm test
```

## License

This project is for educational purposes only.
