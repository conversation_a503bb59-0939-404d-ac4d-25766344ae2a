import { S as SerializedCoverageConfig, a as SerializedConfig } from './chunks/config.d.D2ROskhv.js';
import { R as RuntimeCoverageModuleLoader } from './chunks/coverage.d.S9RMNXIe.js';
import { SerializedDiffOptions } from '@vitest/utils/diff';
import { VitestExecutor } from './execute.js';
export { collectTests, processError, startTests } from '@vitest/runner';
import * as spy from '@vitest/spy';
export { spy as SpyModule };
export { LoupeOptions, ParsedStack, StringifyOptions, format, getSafeTimers, inspect, stringify } from '@vitest/utils';
export { TraceMap, originalPositionFor } from '@vitest/utils/source-map';
import '@vitest/pretty-format';
import '@vitest/snapshot';
import '@vitest/snapshot/environment';
import 'vite-node/client';
import 'vite-node';
import './chunks/worker.d.1GmBbd7G.js';
import './chunks/environment.d.cL3nLXbE.js';
import 'vitest/optional-types.js';
import 'node:vm';
import '@vitest/mocker';
import './chunks/mocker.d.BE_2ls6u.js';

declare function startCoverageInsideWorker(options: SerializedCoverageConfig | undefined, loader: RuntimeCoverageModuleLoader, runtimeOptions: {
	isolate: boolean
}): Promise<unknown>;
declare function takeCoverageInsideWorker(options: SerializedCoverageConfig | undefined, loader: RuntimeCoverageModuleLoader): Promise<unknown>;
declare function stopCoverageInsideWorker(options: SerializedCoverageConfig | undefined, loader: RuntimeCoverageModuleLoader, runtimeOptions: {
	isolate: boolean
}): Promise<unknown>;

declare function setupCommonEnv(config: SerializedConfig): Promise<void>;
declare function loadDiffConfig(config: SerializedConfig, executor: VitestExecutor): Promise<SerializedDiffOptions | undefined>;
declare function loadSnapshotSerializers(config: SerializedConfig, executor: VitestExecutor): Promise<void>;

export { loadDiffConfig, loadSnapshotSerializers, setupCommonEnv, startCoverageInsideWorker, stopCoverageInsideWorker, takeCoverageInsideWorker };
