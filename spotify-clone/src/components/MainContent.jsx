import { ChevronLeft, ChevronRight, User, Play } from 'lucide-react'
import { useMusic } from '../context/MusicContext'
import { recentlyPlayed, madeForYou } from '../data/mockData'

const MainContent = () => {
  const { playSong, selectPlaylist } = useMusic()

  const handlePlaylistClick = (playlist) => {
    selectPlaylist(playlist)
    // In a real app, this would load the playlist's songs
  }

  const handlePlayClick = (item) => {
    // For demo purposes, play the first song when clicking play on a playlist
    playSong({
      id: item.id,
      title: `Song from ${item.title}`,
      artist: 'Various Artists',
      album: item.title,
      duration: 180,
      image: item.image
    })
  }

  return (
    <div className="flex-1 bg-gradient-to-b from-spotify-gray to-spotify-dark-gray overflow-y-auto">
      {/* Header */}
      <header className="flex items-center justify-between p-6 bg-gradient-to-b from-black/50 to-transparent">
        <div className="flex items-center space-x-4">
          <button className="bg-black/70 rounded-full p-2 hover:bg-black/80 transition-colors">
            <ChevronLeft size={20} className="text-white" />
          </button>
          <button className="bg-black/70 rounded-full p-2 hover:bg-black/80 transition-colors">
            <ChevronRight size={20} className="text-white" />
          </button>
        </div>
        <div className="flex items-center space-x-4">
          <button className="bg-black rounded-full p-2 hover:bg-spotify-gray transition-colors">
            <User size={20} className="text-white" />
          </button>
        </div>
      </header>

      {/* Main Content */}
      <main className="px-6 pb-6">
        {/* Greeting */}
        <h1 className="text-3xl font-bold text-white mb-6">Good evening</h1>

        {/* Recently Played */}
        <section className="mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {recentlyPlayed.map((item) => (
              <div key={item.id} className="bg-spotify-gray/30 rounded-md flex items-center hover:bg-spotify-gray/50 transition-colors group cursor-pointer">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-l-md flex items-center justify-center">
                  <span className="text-white font-bold text-lg">{item.title.charAt(0)}</span>
                </div>
                <div className="flex-1 px-4">
                  <h3 className="text-white font-semibold">{item.title}</h3>
                </div>
                <div className="pr-4 opacity-0 group-hover:opacity-100 transition-opacity">
                  <button className="bg-spotify-green rounded-full p-3 hover:scale-105 transition-transform">
                    <Play size={16} className="text-black fill-current ml-1" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Made for You */}
        <section>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold text-white">Made for you</h2>
            <button className="text-spotify-light-gray hover:text-white text-sm font-semibold">Show all</button>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
            {madeForYou.map((item) => (
              <div key={item.id} className="bg-spotify-gray/20 p-4 rounded-lg hover:bg-spotify-gray/40 transition-colors group cursor-pointer">
                <div className="relative mb-4">
                  <div className="w-full aspect-square bg-gradient-to-br from-blue-500 to-purple-600 rounded-md flex items-center justify-center">
                    <span className="text-white font-bold text-2xl">{item.title.charAt(0)}</span>
                  </div>
                  <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button className="bg-spotify-green rounded-full p-3 hover:scale-105 transition-transform shadow-lg">
                      <Play size={16} className="text-black fill-current ml-1" />
                    </button>
                  </div>
                </div>
                <h3 className="text-white font-semibold mb-2 truncate">{item.title}</h3>
                <p className="text-spotify-light-gray text-sm line-clamp-2">{item.description}</p>
              </div>
            ))}
          </div>
        </section>
      </main>
    </div>
  )
}

export default MainContent
