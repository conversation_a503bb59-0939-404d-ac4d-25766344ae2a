import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import App from './App'

describe('Spotify Clone App', () => {
  it('renders the main components', () => {
    render(<App />)
    
    // Check if main sections are present
    expect(screen.getByText('Spotify')).toBeInTheDocument()
    expect(screen.getByText('Home')).toBeInTheDocument()
    expect(screen.getByText('Search')).toBeInTheDocument()
    expect(screen.getByText('Your Library')).toBeInTheDocument()
    expect(screen.getByText('Good evening')).toBeInTheDocument()
    expect(screen.getByText('Made for you')).toBeInTheDocument()
    expect(screen.getByText('Blinding Lights')).toBeInTheDocument()
    expect(screen.getByText('The Weeknd')).toBeInTheDocument()
  })

  it('renders playlist items', () => {
    render(<App />)

    // Check if playlists are rendered (using getAllByText for duplicates)
    expect(screen.getAllByText('Liked Songs')).toHaveLength(3) // Appears in sidebar, main content, and player
    expect(screen.getAllByText('Discover Weekly').length).toBeGreaterThan(0)
    expect(screen.getAllByText('Daily Mix 1').length).toBeGreaterThan(0)
  })

  it('renders player controls', () => {
    render(<App />)
    
    // Check if player controls are present
    const playButtons = screen.getAllByRole('button')
    expect(playButtons.length).toBeGreaterThan(0)
  })
})
