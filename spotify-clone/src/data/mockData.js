// Mock data for the Spotify clone
export const recentlyPlayed = [
  {
    id: 1,
    title: 'Liked Songs',
    type: 'Playlist',
    image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=300&fit=crop&crop=center',
    color: 'from-purple-500 to-pink-500'
  },
  {
    id: 2,
    title: 'Discover Weekly',
    type: 'Made for you',
    image: 'https://images.unsplash.com/photo-1470225620780-dba8ba36b745?w=300&h=300&fit=crop&crop=center',
    color: 'from-green-500 to-blue-500'
  },
  {
    id: 3,
    title: 'Release Radar',
    type: 'Made for you',
    image: 'https://images.unsplash.com/photo-1511671782779-c97d3d27a1d4?w=300&h=300&fit=crop&crop=center',
    color: 'from-blue-500 to-purple-600'
  },
  {
    id: 4,
    title: 'Daily Mix 1',
    type: 'Made for you',
    image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=300&fit=crop&crop=center',
    color: 'from-red-500 to-orange-500'
  },
  {
    id: 5,
    title: 'Chill Hits',
    type: 'Playlist',
    image: 'https://images.unsplash.com/photo-1459749411175-04bf5292ceea?w=300&h=300&fit=crop&crop=center',
    color: 'from-teal-500 to-cyan-500'
  },
  {
    id: 6,
    title: 'Rock Classics',
    type: 'Playlist',
    image: 'https://images.unsplash.com/photo-1498038432885-c6f3f1b912ee?w=300&h=300&fit=crop&crop=center',
    color: 'from-gray-700 to-gray-900'
  },
]

export const madeForYou = [
  {
    id: 1,
    title: 'Daily Mix 1',
    description: 'The Weeknd, Post Malone, Ed Sheeran and more',
    image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=300&fit=crop&crop=center',
    color: 'from-blue-500 to-purple-600'
  },
  {
    id: 2,
    title: 'Daily Mix 2',
    description: 'Drake, Travis Scott, Future and more',
    image: 'https://images.unsplash.com/photo-1470225620780-dba8ba36b745?w=300&h=300&fit=crop&crop=center',
    color: 'from-green-500 to-teal-600'
  },
  {
    id: 3,
    title: 'Daily Mix 3',
    description: 'Billie Eilish, Lorde, Lana Del Rey and more',
    image: 'https://images.unsplash.com/photo-1511671782779-c97d3d27a1d4?w=300&h=300&fit=crop&crop=center',
    color: 'from-pink-500 to-rose-600'
  },
  {
    id: 4,
    title: 'Discover Weekly',
    description: 'Your weekly mixtape of fresh music',
    image: 'https://images.unsplash.com/photo-1459749411175-04bf5292ceea?w=300&h=300&fit=crop&crop=center',
    color: 'from-indigo-500 to-purple-600'
  },
  {
    id: 5,
    title: 'Release Radar',
    description: 'Catch all the latest music from artists you follow',
    image: 'https://images.unsplash.com/photo-1498038432885-c6f3f1b912ee?w=300&h=300&fit=crop&crop=center',
    color: 'from-orange-500 to-red-600'
  },
]

export const songs = [
  {
    id: 1,
    title: "Blinding Lights",
    artist: "The Weeknd",
    album: "After Hours",
    duration: 200,
    image: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=64&h=64&fit=crop&crop=center"
  },
  {
    id: 2,
    title: "Watermelon Sugar",
    artist: "Harry Styles",
    album: "Fine Line",
    duration: 174,
    image: "https://images.unsplash.com/photo-1470225620780-dba8ba36b745?w=64&h=64&fit=crop&crop=center"
  },
  {
    id: 3,
    title: "Levitating",
    artist: "Dua Lipa",
    album: "Future Nostalgia",
    duration: 203,
    image: "https://images.unsplash.com/photo-1511671782779-c97d3d27a1d4?w=64&h=64&fit=crop&crop=center"
  },
  {
    id: 4,
    title: "Good 4 U",
    artist: "Olivia Rodrigo",
    album: "SOUR",
    duration: 178,
    image: "https://images.unsplash.com/photo-1459749411175-04bf5292ceea?w=64&h=64&fit=crop&crop=center"
  },
  {
    id: 5,
    title: "Stay",
    artist: "The Kid LAROI, Justin Bieber",
    album: "F*CK LOVE 3: OVER YOU",
    duration: 141,
    image: "https://images.unsplash.com/photo-1498038432885-c6f3f1b912ee?w=64&h=64&fit=crop&crop=center"
  }
]

export const playlists = [
  'Liked Songs',
  'My Playlist #1',
  'Discover Weekly',
  'Release Radar',
  'Chill Hits',
  'Rock Classics',
  'Pop Mix',
  'Indie Folk',
  'Electronic Vibes',
  'Jazz Essentials',
  'Hip Hop Central',
  'Country Roads',
  'Classical Essentials',
  'Workout Beats',
  'Study Music'
]
