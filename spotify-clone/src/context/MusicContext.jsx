import { createContext, useContext, useReducer } from 'react'
import { songs } from '../data/mockData'

const MusicContext = createContext()

const initialState = {
  currentSong: songs[0],
  isPlaying: false,
  volume: 50,
  isMuted: false,
  progress: 0,
  duration: songs[0].duration,
  queue: songs,
  currentIndex: 0,
  shuffle: false,
  repeat: false,
  searchQuery: '',
  selectedPlaylist: null
}

function musicReducer(state, action) {
  switch (action.type) {
    case 'PLAY_PAUSE':
      return { ...state, isPlaying: !state.isPlaying }
    
    case 'PLAY_SONG':
      const songIndex = state.queue.findIndex(song => song.id === action.payload.id)
      return {
        ...state,
        currentSong: action.payload,
        currentIndex: songIndex,
        isPlaying: true,
        progress: 0,
        duration: action.payload.duration
      }
    
    case 'NEXT_SONG':
      const nextIndex = state.shuffle 
        ? Math.floor(Math.random() * state.queue.length)
        : (state.currentIndex + 1) % state.queue.length
      return {
        ...state,
        currentSong: state.queue[nextIndex],
        currentIndex: nextIndex,
        progress: 0,
        duration: state.queue[nextIndex].duration
      }
    
    case 'PREVIOUS_SONG':
      const prevIndex = state.currentIndex === 0 
        ? state.queue.length - 1 
        : state.currentIndex - 1
      return {
        ...state,
        currentSong: state.queue[prevIndex],
        currentIndex: prevIndex,
        progress: 0,
        duration: state.queue[prevIndex].duration
      }
    
    case 'SET_VOLUME':
      return { ...state, volume: action.payload, isMuted: false }
    
    case 'TOGGLE_MUTE':
      return { ...state, isMuted: !state.isMuted }
    
    case 'SET_PROGRESS':
      return { ...state, progress: action.payload }
    
    case 'TOGGLE_SHUFFLE':
      return { ...state, shuffle: !state.shuffle }
    
    case 'TOGGLE_REPEAT':
      return { ...state, repeat: !state.repeat }
    
    case 'SET_SEARCH_QUERY':
      return { ...state, searchQuery: action.payload }
    
    case 'SELECT_PLAYLIST':
      return { ...state, selectedPlaylist: action.payload }
    
    default:
      return state
  }
}

export function MusicProvider({ children }) {
  const [state, dispatch] = useReducer(musicReducer, initialState)

  const actions = {
    playPause: () => dispatch({ type: 'PLAY_PAUSE' }),
    playSong: (song) => dispatch({ type: 'PLAY_SONG', payload: song }),
    nextSong: () => dispatch({ type: 'NEXT_SONG' }),
    previousSong: () => dispatch({ type: 'PREVIOUS_SONG' }),
    setVolume: (volume) => dispatch({ type: 'SET_VOLUME', payload: volume }),
    toggleMute: () => dispatch({ type: 'TOGGLE_MUTE' }),
    setProgress: (progress) => dispatch({ type: 'SET_PROGRESS', payload: progress }),
    toggleShuffle: () => dispatch({ type: 'TOGGLE_SHUFFLE' }),
    toggleRepeat: () => dispatch({ type: 'TOGGLE_REPEAT' }),
    setSearchQuery: (query) => dispatch({ type: 'SET_SEARCH_QUERY', payload: query }),
    selectPlaylist: (playlist) => dispatch({ type: 'SELECT_PLAYLIST', payload: playlist })
  }

  return (
    <MusicContext.Provider value={{ ...state, ...actions }}>
      {children}
    </MusicContext.Provider>
  )
}

export function useMusic() {
  const context = useContext(MusicContext)
  if (!context) {
    throw new Error('useMusic must be used within a MusicProvider')
  }
  return context
}
