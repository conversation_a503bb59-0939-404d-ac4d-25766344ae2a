const express = require("express");
const mongoose = require("mongoose");
const dotenv = require("dotenv");
const cors = require("cors");
const songRoutes = require("./routes/songs");

dotenv.config();
const app = express();
app.use(cors({
  origin: "http://localhost:5173", // Replace with your frontend URL
}));
app.use(express.json());
app.use("/api/songs", songRoutes);

mongoose
  .connect(process.env.MONGO_URI)
  .then(() => {
    console.log("MongoDB Connected");
    app.listen(5000, () => console.log("Server started on port 5000"));
  })
  .catch((err) => console.log(err));
