import { useState } from "react";
import axios from "axios";

function AddSongForm() {
  const [formData, setFormData] = useState({
    title: "",
    artist: "",
    album: "",
    duration: "",
    audioUrl: "",
    imageUrl: "",
    genre: "",
  });

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await axios.post("http://localhost:5000/api/songs/add", formData);
      console.log("Song added:", response.data);
      alert("Song uploaded successfully!");
    } catch (err) {
      console.error("Error adding song:", err);
      alert("Error uploading song. Please try again.");
    }
  };

  return (
    <form onSubmit={handleSubmit} className="flex flex-col gap-4">
      <input type="text" name="title" placeholder="Title" onChange={handleChange} required />
      <input type="text" name="artist" placeholder="Artist" onChange={handleChange} required />
      <input type="text" name="album" placeholder="Album" onChange={handleChange} />
      <input type="number" name="duration" placeholder="Duration (seconds)" onChange={handleChange} />
      <input type="url" name="audioUrl" placeholder="Audio URL" onChange={handleChange} required />
      <input type="url" name="imageUrl" placeholder="Image URL" onChange={handleChange} />
      <input type="text" name="genre" placeholder="Genre" onChange={handleChange} />
      <button type="submit" className="bg-blue-500 text-white p-2 rounded-lg">Add Song</button>
    </form>
  );
}

export default AddSongForm;
