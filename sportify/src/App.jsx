// import React from 'react';
// import Navbar from './mains.jsx/Navbar';
// import Box from './mains.jsx/Box';
// // Replace react.svg with a placeholder or valid image path;
import ListSongs from "./ListSongs";

function App() {
  return (
    <div className="bg-black h-screen">
      <Navbar />
      <Box />
      <ListSongs />
    </div>
  );
}

export default App;
// ListSongs.jsx
import { useEffect, useState } from "react";
import axios from "axios";

function ListSongs() {
  const [songs, setSongs] = useState([]);

  useEffect(() => {
    axios.get("http://localhost:5000/api/songs").then((res) => {
      setSongs(res.data);
    });
  }, []);

  return (
    <div className="flex flex-col gap-4">
      {songs.map((song) => (
        <div key={song._id} className="bg-gray-200 p-4 rounded-lg">
          <h2>{song.title} - {song.artist}</h2>
          <audio controls src={song.audioUrl}></audio>
        </div>
      ))}
    </div>
  );
}

export default ListSongs;

