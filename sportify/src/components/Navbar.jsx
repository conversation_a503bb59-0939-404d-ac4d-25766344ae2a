import React from 'react';
import { Search, User } from 'lucide-react';

const Navbar = () => {
  return (
    <nav className="bg-black p-4 flex items-center justify-between border-b border-gray-800">
      <div className="flex items-center space-x-4">
        <h1 className="text-white text-2xl font-bold text-green-500">Devesh</h1>
      </div>
      
      <div className="flex-1 max-w-md mx-8">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search for songs, artists, or albums"
            className="w-full bg-gray-800 text-white pl-10 pr-4 py-2 rounded-full focus:outline-none focus:ring-2 focus:ring-green-500"
          />
        </div>
      </div>
      
      <div className="flex items-center space-x-4">
        <button className="text-gray-400 hover:text-white transition-colors">
          <User size={24} />
        </button>
      </div>
    </nav>
  );
};

export default Navbar;
